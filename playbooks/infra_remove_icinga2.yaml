systemctl stop icinga2
systemctl disable icinga2

rm /usr/lib/systemd/system/icinga2.service
rm /etc/systemd/system/icinga2.service
rm -rf /etc/systemd/system/icinga2.service.d

update-rc.d icinga2 remove >/dev/null

apt remove icinga2 icinga2-common icinga2-bin -y

rm -rf /run/icinga2
rm -rf /etc/icinga2
rm -rf /var/log/icinga2
rm -rf /var/lib/icinga2
rm -rf /usr/lib/icinga2
rm -rf /var/spool/icinga2
rm -rf /var/cache/icinga2
rm -rf /usr/share/icinga2
rm -rf /usr/sbin/icinga2
rm -rf /usr/share/doc/icinga*

rm /etc/init.d/icinga2
rm /etc/logrotate.d/icinga2
rm /var/lib/systemd/deb-systemd-helper-masked/icinga2.service*
rm /etc/default/icinga2
rm /usr/local/bin/icinga2_auto_ack
rm /usr/share/keyrings/icinga-archive-keyring.asc
rm /var/lib/dpkg/info/icinga2*
rm /usr/share/bash-completion/completions/icinga2
rm /var/lib/apt/lists/packages.icinga.com_ubuntu_dists_icinga-jammy_InRelease
rm /var/lib/apt/lists/packages.icinga.com_ubuntu_dists_icinga-jammy_main_binary-amd64_Packages
rm /var/lib/systemd/deb-systemd-helper-enabled/multi-user.target.wants/icinga2.service
rm /var/lib/systemd/deb-systemd-helper-enabled/icinga2.service.dsh-also