---
- name: "Complete removal of Icinga2 monitoring system"
  become: true
  gather_facts: true
  hosts: "{{ run_hosts.split(',') }}"

  vars:
    run_hosts: "localhost"
    create_backup: "{{ backup_configs | default(true) }}"
    backup_directory: "/tmp/icinga2_backup_{{ ansible_date_time.epoch }}"

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: true

  pre_tasks:
    - name: Create backup directory
      file:
        path: "{{ backup_directory }}"
        state: directory
        mode: '0755'
      when: create_backup | bool

    - name: Backup Icinga2 configuration files
      copy:
        src: "{{ item }}"
        dest: "{{ backup_directory }}/"
        remote_src: true
        mode: preserve
      loop:
        - /etc/icinga2/
        - /var/lib/icinga2/
        - /var/log/icinga2/
      ignore_errors: true
      when: create_backup | bool

    - name: Display backup location
      debug:
        msg: "Icinga2 configuration backup created at: {{ backup_directory }}"
      when: create_backup | bool

  tasks:
    # ===== STOP AND DISABLE ICINGA2 SERVICE =====
    - name: Stop Icinga2 service
      systemd:
        name: icinga2
        state: stopped
        enabled: false
      ignore_errors: true

    # ===== REMOVE SYSTEMD SERVICE FILES =====
    - name: Remove Icinga2 systemd service files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /usr/lib/systemd/system/icinga2.service
        - /etc/systemd/system/icinga2.service
        - /etc/systemd/system/icinga2.service.d
      notify: reload systemd

    # ===== REMOVE SYSV INIT CONFIGURATION =====
    - name: Remove Icinga2 from SysV init
      command: update-rc.d icinga2 remove
      ignore_errors: true

    # ===== REMOVE ICINGA2 PACKAGES =====
    - name: Remove Icinga2 packages
      apt:
        name:
          - icinga2
          - icinga2-common
          - icinga2-bin
        state: absent
        purge: true
        autoremove: true
      ignore_errors: true

    # ===== REMOVE ICINGA2 DIRECTORIES =====
    - name: Remove Icinga2 runtime and configuration directories
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /run/icinga2
        - /etc/icinga2
        - /var/log/icinga2
        - /var/lib/icinga2
        - /usr/lib/icinga2
        - /var/spool/icinga2
        - /var/cache/icinga2
        - /usr/share/icinga2
        - /usr/sbin/icinga2
      ignore_errors: true

    # ===== REMOVE ICINGA2 DOCUMENTATION =====
    - name: Remove Icinga2 documentation directories
      shell: rm -rf /usr/share/doc/icinga*
      ignore_errors: true

    # ===== REMOVE ICINGA2 CONFIGURATION AND SUPPORT FILES =====
    - name: Remove Icinga2 configuration and support files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/init.d/icinga2
        - /etc/logrotate.d/icinga2
        - /etc/default/icinga2
        - /usr/local/bin/icinga2_auto_ack
        - /usr/share/keyrings/icinga-archive-keyring.asc
        - /usr/share/bash-completion/completions/icinga2
      ignore_errors: true

    # ===== REMOVE SYSTEMD HELPER FILES =====
    - name: Remove systemd helper files
      shell: rm -f {{ item }}
      loop:
        - /var/lib/systemd/deb-systemd-helper-masked/icinga2.service*
        - /var/lib/systemd/deb-systemd-helper-enabled/multi-user.target.wants/icinga2.service
        - /var/lib/systemd/deb-systemd-helper-enabled/icinga2.service.dsh-also
      ignore_errors: true

    # ===== REMOVE DPKG INFO FILES =====
    - name: Remove dpkg info files
      shell: rm -f /var/lib/dpkg/info/icinga2*
      ignore_errors: true

    # ===== REMOVE APT CACHE FILES =====
    - name: Remove APT cache files for Icinga2
      shell: rm -f {{ item }}
      loop:
        - /var/lib/apt/lists/packages.icinga.com_ubuntu_dists_icinga-jammy_InRelease
        - /var/lib/apt/lists/packages.icinga.com_ubuntu_dists_icinga-jammy_main_binary-amd64_Packages
      ignore_errors: true

    # ===== CLEAN UP APT CACHE =====
    - name: Clean APT cache
      apt:
        autoclean: true
        autoremove: true
      ignore_errors: true

  post_tasks:
    - name: Verify Icinga2 removal
      command: which icinga2
      register: icinga2_check
      failed_when: icinga2_check.rc == 0
      ignore_errors: true

    - name: Display removal status
      debug:
        msg: "{{ 'Icinga2 successfully removed' if icinga2_check.rc != 0 else 'Warning: Icinga2 binary still found' }}"

    - name: Force systemd daemon reload
      systemd:
        daemon_reload: true