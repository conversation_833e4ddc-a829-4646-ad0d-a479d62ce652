---
softether_download_link: https://github.com/SoftEtherVPN/SoftEtherVPN_Stable/releases/download/v4.38-9760-rtm/softether-vpnclient-v4.38-9760-rtm-2021.08.17-linux-x64-64bit.tar.gz
softether_server_ip: vpn.eyecue.fingermark.co.nz
softether_port_number: 443
softether_dest_dir: /usr/local
softether_server_passwd: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          36356236633162396632643765333633646235613533656438636530353834623634663536313331
          6661396466323465653236633462376531356533653133660a646432623931666162613039666335
          36353239323834386162356339663863363134626439326334356365396634326339336535393664
          3565343438653433650a333035366565383530353239646331346666323433306162626435623833
          30383465343537353833613835393061333438363531393864386366613366653132
softether_virtualhub: "{{ customer | trim }}"
softether_server_vpncmd: "/usr/local/vpnclient/vpncmd /SERVER {{ softether_server_ip }}:{{ softether_port_number }} /PASSWORD:{{ softether_server_passwd }} /ADMINHUB:'{{ softether_virtualhub | trim }}' /CMD"
softether_server_create_account: yes
softether_user_realname: "{{ inventory_hostname | replace('_', ' ') }}"
softether_client_vpn_nic_name: "sfteth"
softether_dns_server: **************
softether_subdomain: "eyeq.vpn"
softether_username: "{{ ansible_hostname }}"
