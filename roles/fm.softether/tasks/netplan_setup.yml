- name: Configure netplan for VPN with DHCP
  template:
    src: netplan_config_dhcp.yaml
    dest: /etc/netplan/02-softether.yaml
  when: softether_virtualhub != "servers"  

- name: Configure netplan for VPN with Static IP
  template:
    src: netplan_config_nodhcp.yaml
    dest: /etc/netplan/02-softether.yaml
  when: softether_virtualhub == "servers" or softether_virtualhub == "SERVERS"

- shell: "vpnclient stop && vpnclient start"

- name: Apply network configuration
  command: netplan apply
