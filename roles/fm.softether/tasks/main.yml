---

- name: Installing dependencies
  apt:
    name:
    - gcc
    - make
    - net-tools
    - unzip
    state: present    
    update-cache: yes    

- name: Checking if Softether is installed
  stat:
    path: "{{ softether_dest_dir }}/vpnclient"
  register: softether_directory

- name: Installing Softether
  include_tasks: install.yml
  when: not softether_directory.stat.exists

- name: Checking if user already exists on the VPN server {{ softether_server_ip }}
  command: "{{ softether_server_vpncmd }} UserGet {{ softether_username }}"
  register: vpn_users_exists
  no_log: true
  ignore_errors: true

- name: "Creating VPN User"
  include_tasks: create_vpn_user.yml
  when: vpn_users_exists.failed and softether_server_create_account

- name: Creating local Account
  include_tasks: create_local_account.yml

- name: Setup Fingermark Server network
  include_tasks: netplan_setup.yml
