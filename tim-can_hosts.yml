all:
  children:
    production:
      children:
        415_danforth_rd:
          hosts:
            fm-tim-can-00002.eyeq.vpn: {}
          vars:
            address: 415 Danforth Rd, Scarborough, ON M1L 2X8, Canada
            country: can
            country_code: '124'
            customer: tim
            deployment_env: Production
            display_name: 415 Danforth Rd
            id: '00002'
            latitude: '61.3765600742'
            longitude: '-98.2945373672'
            serial_number: MZ00V68P
            site_contact_email: <EMAIL>
            site_id: fm-tim-can-00002
            timezone: America/Toronto
        9600_islington_avenue:
          hosts:
            fm-tim-can-00001.eyeq.vpn: {}
          vars:
            address: 9600 Islington Ave Bldg D Unit 3, Woodbridge, ON L4H 2T1, Canada
            country: can
            country_code: '124'
            customer: tim
            deployment_env: Production
            display_name: 9600 Islington Avenue
            id: '00001'
            latitude: '61.3765600742'
            longitude: '-98.2945373672'
            serial_number: MZ00V68N
            site_contact_email: <EMAIL>
            site_id: fm-tim-can-00001
            timezone: America/Toronto
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-tim-can-helm
        aws_s3_bucket_camera_images: eyecue-tim-can-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: tim-can
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
