all:
  children:
    production:
      children:
        79th_ct_miami_lakes:
          hosts:
            fm-bkg-usa-07553.eyeq.vpn: {}
          vars:
            address: 15320 NW 79th Ct, Miami Lakes, FL 33016
            country: usa
            country_code: '840'
            customer: bkg
            deployment_env: Production
            display_name: 79Th Ct Miami Lakes
            id: '07553'
            latitude: '45.6875333395'
            longitude: '-112.494333916'
            serial_number: MZ0165N7
            site_contact_email: <EMAIL>
            site_id: fm-bkg-usa-07553
            timezone: America/New_York
        7th_street_miami:
          hosts:
            fm-bkg-usa-00064.eyeq.vpn: {}
          vars:
            address: 5721 NW 7th St, Miami, FL 33126, United States
            country: usa
            country_code: '840'
            customer: bkg
            deployment_env: Production
            display_name: 7th Street Miami
            id: '00064'
            latitude: '25.7781971'
            longitude: '-80.289434220'
            serial_number: MZ0165N8
            site_contact_email: <EMAIL>
            site_id: fm-bkg-usa-00064
            timezone: America/New_York
        9th_street_miami:
          hosts:
            fm-bkg-usa-00010.eyeq.vpn: {}
          vars:
            address: NW 9th St, Miami, FL 33126, United States
            country: usa
            country_code: '840'
            customer: bkg
            deployment_env: Production
            display_name: 9th Street Miami
            id: '00010'
            latitude: '25.7814757'
            longitude: '-80.2329513'
            serial_number: MJ0LFQKF
            site_contact_email: <EMAIL>
            site_id: fm-bkg-usa-00010
            timezone: America/New_York
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-bkg-usa-helm
        aws_s3_bucket_camera_images: eyecue-bkg-usa-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: bkg-usa
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
