# Remote Setup for Eyecue Servers


Clone this repository on you computer

copy ssh_conf file into your ssh folder

```
cp ssh_conf /home/<USER>/.ssh/conf
```

After you copy the file try if you can ssh to the Eyecue server from your computer
Try:

```
ssh hastingsprod
```

If it was successfully, now you can deploy the setup using Ansible roles.

Install the tools needed on you computer

```
apt update
apt install python3-pip -y
pip3 install ansible
```

cd into the repository

```
cd eyecue-server-setup
```

Generate a new inventory using the Python script. You will need your 1Password credentials and secret. Also a copy of fingermark.id_rsa private key.

```
python3 gen_inventory.py
```

Select the site you want to deploy and write it down on the Ansible playbook

```
code playbooks/06_onsite_setup_server.yaml
```

Modify the ```hosts``` key

### Examples:

Deploying locally

```
Hosts: localhost
connection: local
```

Deploying to all MCD

```
Hosts: mcd
```

Or multiple groups

```
Hosts:
- mcd
- kfc
```

Deploying individual hosts:

```
Hosts:
- hastingsprod
- brookvale
- rbi0006
- ...
```


Run the playbook
```
ansible-playbook -i hosts.yaml playbooks/setup_server.yaml --ask-vault-pass
```

This playbook should install NVidia drivers, Kubernetes, Docker and ArgoCD (The new site should be already created on the Bitbucket repository)

#### See also

* fingermark.meshcmd role
* fingermark.cv-monitoring

## Docker container
You can now run ansible from inside a Docker container (make sure you set the .env file first)

```shell
cp .env.dist .env
nano .env
make build
make shell
ansible --version
```

# New Customer

1. Create a new inventory using the customer's acronym: xyz_hosts.yaml
```
all:
  vars:
    ansible_python_interpreter: "/usr/bin/python3"
    fingermark_product: "eyecue"
    customer: "xyz"
    kubeadmin_config: "/etc/kubernetes/admin.conf"
  children:
    production:
      vars:
        pod_network_cidr: "**********/16"
        meshid: ""
        aws_s3_bucket_camera_images: "eyecue-xyz-us-camera-images"
        argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-xyz-helm"
      children:
        add_me:
```
