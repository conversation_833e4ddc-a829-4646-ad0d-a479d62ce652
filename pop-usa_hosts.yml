all:
  children:
    production:
      children:
        williams_blvd:
          hosts:
            fm-pop-usa-02023.eyeq.vpn: {}
          vars:
            address: 3444 Williams Blvd Kenner LA 70065
            country: usa
            country_code: '840'
            customer: pop
            deployment_env: Production
            display_name: <PERSON> Blvd
            id: '02023'
            latitude: '45.6875333395'
            longitude: '-112.494333916'
            serial_number: MXL31336YC
            site_contact_email: <EMAIL>
            site_id: fm-pop-usa-02023
            timezone: America/Chicago
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-pop-usa-helm
        aws_s3_bucket_camera_images: eyecue-pop-usa-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: pop-usa
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
