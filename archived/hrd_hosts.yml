all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
    fingermark_product: eyecue
    customer: hrd 
    kubeadmin_config: "/etc/kubernetes/admin.conf"    
  children:
    production:
      vars:        
        pod_network_cidr: "**********/16"        
        meshid: "XbnehF9XD7XYjxHy"
        argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-amr-helm"
      children:                
        # nshama_server: (MOVED TO POC)
        #   hosts:
        #       fm-hrd-uae-001.eyeq.vpn
        #   vars:
        #     coordinates: "25.010543,55.289622"
        #     display_name: "<PERSON>sham<PERSON>"
        #     timezone:
