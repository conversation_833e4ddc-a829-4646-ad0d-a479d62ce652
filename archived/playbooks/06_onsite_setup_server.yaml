---
 # To use this role, add --ask-vault-pass on the ansible-playbook command
- name: Fingermark Computer Vision Server

  # Note: Please use ./bin/playbook-run.sh (prompts for inputs OR use the below)
  # i.e. ./bin/playbook-run.sh "jeremy" "06_onsite_setup_server.yaml" "dev_hosts.yml" "fm-tst-nz-4242"
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"

  become: true

  roles:
    - role: timezone
      tags: 
        - basics

    - role: netplan_setup
      tags: 
        - network
      vars:
        netplan_server_ip: "***********/24"
        netplan_default_gateway: "***********"
        netplan_apply_new_config: yes
        netplan_config_static: false
    - role: kubernetes/master
      tags: 
        - k8s
      vars:
        kube_version: "v1.20.0"
        kubeadm_opts: "--apiserver-advertise-address '*************'"

    - role: calico
      tags: 
        - k8s
        - calico
      vars:
        calico_manifest: /tmp/calico.yaml

    - role: argocd
      tags: 
        - application
        - argocd
      vars:
        delete_eyecue_app: no
        remove_argocd_ns: no
        argocd_bin_update: no
        destination_manifest: /opt/argocd/

    - role: fingermark.kube-monitoring
      tags: 
        - monitoring
        - k8s
        - application
      vars:
        thanos_upgrade: no
        thanos_sidecar_install: yes
        node_exporter_standalone_version: no  
        # LOGGING
        monitoring_install_logging: yes
        delete_logging_ns: no
        
    - role: geerlingguy.security
      tags: 
        - security

    - role: fm.ubuntu-hardening
      tags: 
        - security
