all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
    fingermark_product: eyecue
    customer: amr
    kubeadmin_config: "/etc/kubernetes/admin.conf"    
  children:
    production:
      vars:        
        pod_network_cidr: "**********/16"        
        meshid: "XbnehF9XD7XYjxHy"
        argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-amr-helm"
      children:                
        # dubai_server: (MOVED TO POC)
        #   hosts:
        #       fm-amr-uae-001.eyeq.vpn
        #   vars:
        #     coordinates: "25.254059,55.302948"
        #     display_name: "Dubai"
        #     timezone:
