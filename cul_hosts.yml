all:
  children:
    infrastructure:
      children:
        wireguard_server:
          hosts: wireguard-server.cul-usa.infra.fingermark.tech
          vars:
            ansible_ssh_private_key_file: ~/Documents/keys/ssh/fm-infra-team.pem
            ansible_user: ubuntu
    production:
      children:
        marie_ave:
          hosts:
            fm-cul-usa-00240.eyeq.vpn: {}
          vars:
            address: 125 Marie Ave E, West St Paul, MN 55118
            country: usa
            country_code: '840'
            customer: cul
            deployment_env: Production
            display_name: Marie Ave
            id: '00240'
            latitude: '45.6875333395'
            longitude: '-112.494333916'
            serial_number: MJ0LFQKN
            site_contact_email: <EMAIL>
            site_id: fm-cul-usa-00240
            timezone: America/Chicago
        oleary_lane:
          hosts:
            fm-cul-usa-00189.eyeq.vpn: {}
          vars:
            address: 3445 OLeary Lane, Eagan, MN 55123
            country: usa
            country_code: '840'
            customer: cul
            deployment_env: Production
            display_name: Oleary Lane
            id: 00189
            latitude: '45.6875333395'
            longitude: '-112.494333916'
            serial_number: MJ0LDRLE
            site_contact_email: <EMAIL>
            site_id: fm-cul-usa-00189
            timezone: America/Chicago
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-cul-usa-helm
        aws_s3_bucket_camera_images: eyecue-cul-usa-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: cul
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
