all:
  children:
    production:
      children:
        danforth:
          hosts:
            fm-mcd-can-04463.eyeq.vpn: {}
          vars:
            address: 1735 Danforth Avenue Toronto On M4C 1H9
            country: can
            country_code: '124'
            customer: mcd
            deployment_env: Production
            display_name: Danforth
            id: '04463'
            latitude: '61.3765600742'
            longitude: '-98.2945373672'
            serial_number: MZ00V68L
            site_contact_email: <EMAIL>
            site_id: fm-mcd-can-04463
            timezone: America/Toronto
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-mcd-can-helm
        aws_s3_bucket_camera_images: eyecue-mcd-can-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: mcd-can
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
